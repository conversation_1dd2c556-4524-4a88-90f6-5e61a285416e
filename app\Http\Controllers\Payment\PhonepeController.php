<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CombinedOrder;
use App\Models\CustomerPackage;
use App\Models\SellerPackage;
use App\Models\BusinessSetting;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\CustomerPackageController;
use App\Http\Controllers\SellerPackageController;
use App\Http\Controllers\WalletController;
use Session;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class PhonepeController extends Controller
{
    private $merchantId;
    private $saltKey;
    private $saltIndex;
    private $apiEndpoint;
    private $isSandbox;

    public function __construct()
    {
        // Use test credentials from memories
        $this->merchantId = env('PHONEPE_MERCHANT_ID', 'TEST-M22BVU1GWA25J_25051');
        $this->saltKey = env('PHONEPE_SALT_KEY', 'NjY4OTk3OGEtMTAwOS00ZDE1LTgzNjEtMDJlNzg3M2RmNWFj');
        $this->saltIndex = env('PHONEPE_SALT_INDEX', '1');

        // Set API endpoint based on sandbox mode
        $sandboxSetting = BusinessSetting::where('type', 'phonepe_sandbox')->first();
        $this->isSandbox = $sandboxSetting ? $sandboxSetting->value == 1 : true;

        if ($this->isSandbox) {
            $this->apiEndpoint = 'https://api-preprod.phonepe.com/apis/pg-sandbox';
        } else {
            $this->apiEndpoint = 'https://api.phonepe.com/apis/hermes';
        }

        // Validate required credentials
        if (!$this->merchantId || !$this->saltKey) {
            Log::error('PhonePe credentials missing: ', [
                'merchantId' => $this->merchantId,
                'saltKey' => $this->saltKey ? 'SET' : 'NOT SET'
            ]);
        }
    }

    /**
     * Generate X-VERIFY header for PhonePe API
     */
    private function generateXVerifyHeader($payload)
    {
        $base64Payload = base64_encode($payload);
        $string = $base64Payload . '/pg/v1/pay' . $this->saltKey;
        $sha256 = hash('sha256', $string);
        return $sha256 . '###' . $this->saltIndex;
    }

    /**
     * Generate X-VERIFY header for status check
     */
    private function generateStatusXVerifyHeader($merchantTransactionId)
    {
        $string = '/pg/v1/status/' . $this->merchantId . '/' . $merchantTransactionId . $this->saltKey;
        $sha256 = hash('sha256', $string);
        return $sha256 . '###' . $this->saltIndex;
    }

    public function pay(Request $request = null)
    {
        try {
            // Validate PhonePe credentials
            if (!$this->merchantId || !$this->saltKey) {
                Log::error('PhonePe credentials not configured properly');
                flash(translate('Payment gateway not configured properly'))->error();
                return redirect()->route('home');
            }

            if (!Session::has('payment_type')) {
                flash(translate('Payment session expired'))->error();
                return redirect()->route('home');
            }

            $paymentType = Session::get('payment_type');
            $amount = 0;
            $merchantTransactionId = 'MT' . time() . rand(1000, 9999);

            // Store transaction ID in session for callback verification
            Session::put('phonepe_transaction_id', $merchantTransactionId);

            switch ($paymentType) {
                case 'cart_payment':
                    $combined_order = CombinedOrder::findOrFail(Session::get('combined_order_id'));
                    $amount = round($combined_order->grand_total * 100); // Amount in paise
                    break;

                case 'wallet_payment':
                    $amount = round(Session::get('payment_data')['amount'] * 100); // Amount in paise
                    break;

                case 'customer_package_payment':
                    $customer_package = CustomerPackage::findOrFail(Session::get('payment_data')['customer_package_id']);
                    $amount = round($customer_package->amount * 100); // Amount in paise
                    break;

                case 'seller_package_payment':
                    $seller_package = SellerPackage::findOrFail(Session::get('payment_data')['seller_package_id']);
                    $amount = round($seller_package->amount * 100); // Amount in paise
                    break;

                default:
                    flash(translate('Invalid payment type'))->error();
                    return redirect()->route('home');
            }

            // Create payment request
            $response = $this->initiatePayment($amount, $merchantTransactionId);

            if (isset($response['success']) && $response['success'] && isset($response['data']['instrumentResponse']['redirectInfo']['url'])) {
                $redirectUrl = $response['data']['instrumentResponse']['redirectInfo']['url'];
                return redirect($redirectUrl);
            } else {
                $errorMessage = isset($response['message']) ? $response['message'] : 'Payment initiation failed';
                Log::error('PhonePe Payment Error: ' . json_encode($response));
                flash(translate('Payment failed: ' . $errorMessage))->error();
                return redirect()->route('home');
            }

        } catch (\Exception $e) {
            Log::error('PhonePe Payment Exception: ' . $e->getMessage());
            flash(translate('Something went wrong during payment'))->error();
            return redirect()->route('home');
        }
    }

    private function initiatePayment($amount, $merchantTransactionId)
    {
        try {
            $callbackUrl = route('phonepe.callback');
            $webhookUrl = route('phonepe.webhook');

            $payload = [
                'merchantId' => $this->merchantId,
                'merchantTransactionId' => $merchantTransactionId,
                'merchantUserId' => 'MUID' . auth()->id() ?? 'GUEST',
                'amount' => $amount,
                'redirectUrl' => $callbackUrl,
                'redirectMode' => 'POST',
                'callbackUrl' => $webhookUrl,
                'paymentInstrument' => [
                    'type' => 'PAY_PAGE'
                ]
            ];

            $jsonPayload = json_encode($payload);
            $base64Payload = base64_encode($jsonPayload);
            $xVerify = $this->generateXVerifyHeader($jsonPayload);

            // Make API request
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'X-VERIFY' => $xVerify
            ])->post($this->apiEndpoint . '/pg/v1/pay', [
                'request' => $base64Payload
            ]);

            $responseData = $response->json();

            Log::info('PhonePe Payment Request: ' . json_encode([
                'payload' => $payload,
                'base64Payload' => $base64Payload,
                'xVerify' => $xVerify,
                'response' => $responseData
            ]));

            return $responseData;

        } catch (\Exception $e) {
            Log::error('PhonePe Payment Initiation Error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function callback(Request $request)
    {
        try {
            Log::info('PhonePe Callback Request: ' . json_encode($request->all()));

            // Get merchant transaction ID from request
            $merchantTransactionId = $request->input('transactionId') ?: $request->input('merchantTransactionId');

            if (!$merchantTransactionId) {
                // Try to get transaction ID from session as fallback
                $merchantTransactionId = Session::get('phonepe_transaction_id');
            }

            if (!$merchantTransactionId) {
                Log::error('PhonePe Callback: No transaction ID found in request: ' . json_encode($request->all()));
                flash(translate('Invalid payment response'))->error();
                return redirect()->route('home');
            }

            // Verify payment status
            $response = $this->checkPaymentStatus($merchantTransactionId);

            Log::info('PhonePe Status Check Response: ' . json_encode($response));

            if (isset($response['success']) && $response['success'] &&
                isset($response['data']['state']) && $response['data']['state'] === 'COMPLETED') {

                $payment_details = json_encode($response);

                // Clear the transaction ID from session
                Session::forget('phonepe_transaction_id');

                if (Session::has('payment_type')) {
                    $paymentType = Session::get('payment_type');

                    switch ($paymentType) {
                        case 'cart_payment':
                            return (new CheckoutController)->checkout_done(Session::get('combined_order_id'), $payment_details);

                        case 'wallet_payment':
                            return (new WalletController)->wallet_payment_done(Session::get('payment_data'), $payment_details);

                        case 'customer_package_payment':
                            return (new CustomerPackageController)->purchase_payment_done(Session::get('payment_data'), $payment_details);

                        case 'seller_package_payment':
                            return (new SellerPackageController)->purchase_payment_done(Session::get('payment_data'), $payment_details);
                    }
                }
            }

            $errorMessage = isset($response['message']) ? $response['message'] : 'Payment verification failed';
            Log::error('PhonePe Payment Failed: ' . $errorMessage);
            flash(translate('Payment failed: ' . $errorMessage))->error();
            return redirect()->route('home');

        } catch (\Exception $e) {
            Log::error('PhonePe Callback Exception: ' . $e->getMessage());
            flash(translate('Payment verification failed'))->error();
            return redirect()->route('home');
        }
    }

    private function checkPaymentStatus($merchantTransactionId)
    {
        try {
            $xVerify = $this->generateStatusXVerifyHeader($merchantTransactionId);

            // Make API request
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'X-VERIFY' => $xVerify,
                'X-MERCHANT-ID' => $this->merchantId
            ])->get($this->apiEndpoint . '/pg/v1/status/' . $this->merchantId . '/' . $merchantTransactionId);

            $responseData = $response->json();

            Log::info('PhonePe Status Check: ' . json_encode([
                'merchantTransactionId' => $merchantTransactionId,
                'xVerify' => $xVerify,
                'response' => $responseData
            ]));

            return $responseData;

        } catch (\Exception $e) {
            Log::error('PhonePe Status Check Error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Handle PhonePe webhook notifications
     */
    public function webhook(Request $request)
    {
        try {
            Log::info('PhonePe Webhook Request: ' . json_encode($request->all()));

            $response = $request->input('response');
            if (!$response) {
                Log::error('PhonePe Webhook: No response data found');
                return response('Invalid webhook data', 400);
            }

            // Decode the base64 response
            $decodedResponse = base64_decode($response);
            $responseData = json_decode($decodedResponse, true);

            if (!$responseData) {
                Log::error('PhonePe Webhook: Failed to decode response data');
                return response('Invalid response format', 400);
            }

            Log::info('PhonePe Webhook Decoded Response: ' . json_encode($responseData));

            // Verify the webhook signature
            $xVerify = $request->header('X-VERIFY');
            if (!$this->verifyWebhookSignature($response, $xVerify)) {
                Log::error('PhonePe Webhook: Signature verification failed');
                return response('Signature verification failed', 400);
            }

            // Process the payment status
            if (isset($responseData['success']) && $responseData['success'] &&
                isset($responseData['data']['state']) && $responseData['data']['state'] === 'COMPLETED') {

                $merchantTransactionId = $responseData['data']['merchantTransactionId'];
                Log::info('PhonePe Webhook: Payment completed for transaction: ' . $merchantTransactionId);

                // Here you can add additional processing if needed
                // For now, we'll just log the successful payment
            }

            return response('OK', 200);

        } catch (\Exception $e) {
            Log::error('PhonePe Webhook Exception: ' . $e->getMessage());
            return response('Webhook processing failed', 500);
        }
    }

    /**
     * Verify webhook signature
     */
    private function verifyWebhookSignature($response, $xVerify)
    {
        if (!$xVerify) {
            return false;
        }

        $parts = explode('###', $xVerify);
        if (count($parts) !== 2) {
            return false;
        }

        $receivedHash = $parts[0];
        $saltIndex = $parts[1];

        if ($saltIndex !== $this->saltIndex) {
            return false;
        }

        $string = $response . $this->saltKey;
        $expectedHash = hash('sha256', $string);

        return hash_equals($expectedHash, $receivedHash);
    }

    public function credentials_index()
    {
        return view('backend.setup_configurations.phonepe_credential');
    }

    public function update_credentials(Request $request)
    {
        $request->validate([
            'PHONEPE_MERCHANT_ID' => 'required|string',
            'PHONEPE_SALT_KEY' => 'required|string',
            'PHONEPE_SALT_INDEX' => 'required|string'
        ]);

        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'phonepe_sandbox')->first();

        if ($business_settings != null) {
            if ($request->has('phonepe_sandbox')) {
                $business_settings->value = 1;
                $business_settings->save();
            } else {
                $business_settings->value = 0;
                $business_settings->save();
            }
        } else {
            $business_settings = new BusinessSetting;
            $business_settings->type = 'phonepe_sandbox';
            $business_settings->value = $request->has('phonepe_sandbox') ? 1 : 0;
            $business_settings->save();
        }

        flash(translate("PhonePe settings updated successfully"))->success();
        return back();
    }

    private function overWriteEnvFile($type, $val)
    {
        $path = base_path('.env');
        if (file_exists($path)) {
            $val = '"'.trim($val).'"';
            if(is_numeric(strpos(file_get_contents($path), $type)) && strpos(file_get_contents($path), $type) >= 0){
                file_put_contents($path, str_replace(
                    $type.'="'.env($type).'"', $type.'='.$val, file_get_contents($path)
                ));
            }
            else{
                file_put_contents($path, file_get_contents($path)."\r\n".$type.'='.$val);
            }
        }
    }
}
