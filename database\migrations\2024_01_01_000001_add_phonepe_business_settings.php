<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\BusinessSetting;

class AddPhonepeBusinessSettings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add PhonePe activation setting
        $phonepe_activation = BusinessSetting::where('type', 'phonepe')->first();
        if (!$phonepe_activation) {
            $phonepe_activation = new BusinessSetting;
            $phonepe_activation->type = 'phonepe';
            $phonepe_activation->value = '0';
            $phonepe_activation->save();
        }

        // Add PhonePe sandbox setting
        $phonepe_sandbox = BusinessSetting::where('type', 'phonepe_sandbox')->first();
        if (!$phonepe_sandbox) {
            $phonepe_sandbox = new BusinessSetting;
            $phonepe_sandbox->type = 'phonepe_sandbox';
            $phonepe_sandbox->value = '1';
            $phonepe_sandbox->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        BusinessSetting::where('type', 'phonepe')->delete();
        BusinessSetting::where('type', 'phonepe_sandbox')->delete();
    }
}
